<!DOCTYPE html>
<html lang="en">
<!-- Mirrored from saaspik.pixelsigns.art/saaspik/contact.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:29 GMT -->

<head>
  <!-- Meta Data -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
  <title>Contact - arkHr</title>

  <link rel="apple-touch-icon" sizes="180x180" href="assets/img/fav/clustark.png" />

  <link rel="icon" type="image/png" sizes="32x32" href="assets/img/fav/clustark.png" />

  <link rel="icon" type="image/png" sizes="16x16" href="assets/img/fav/clustark.png" />

  <link rel="mask-icon" href="assets/img/fav/clustark.png" color="#7052fb" />

  <meta name="msapplication-TileColor" content="#fa7070" />
  <meta name="theme-color" content="#fa7070" />

  <!-- Dependency Styles -->
  <link rel="stylesheet" href="dependencies/bootstrap/css/bootstrap.min.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/fontawesome/css/all.min.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/swiper/css/swiper.min.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/wow/css/animate.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/magnific-popup/css/magnific-popup.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/components-elegant-icons/css/elegant-icons.min.css" type="text/css" />
  <link rel="stylesheet" href="dependencies/simple-line-icons/css/simple-line-icons.css" type="text/css" />

  <!-- Site Stylesheet -->
  <link rel="stylesheet" href="assets/css/app.css" type="text/css" />
  <link rel="stylesheet" href="assets/css/terms.css" type="text/css" />

  <!-- Google Web Fonts -->
  <link rel="preconnect" href="https://fonts.gstatic.com/" />
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&amp;family=Satisfy&amp;display=swap"
    rel="stylesheet" />

  <!-- google analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y7122VDXV0"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-Y7122VDXV0");
  </script>
  <!-- google analytics end -->
</head>

<body id="home-version-1" class="home-version-1" data-style="default">
  <div class="" id="app">
    <a href="#main_content" data-type="section-switch" class="return-to-top">
      <i class="fa fa-chevron-up"></i>
    </a>

    <div class="page-loader">
      <div class="loader">
        <!-- Loader -->
        <div class="blobs">
          <div class="blob-center"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
          <defs>
            <filter id="goo">
              <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
              <feColorMatrix in="blur" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="goo" />
              <feBlend in="SourceGraphic" in2="goo" />
            </filter>
          </defs>
        </svg>
      </div>
    </div>
    <!-- /.page-loader -->

    <div id="main_content">
      <!--=========================-->
      <!--=        Navbar         =-->
      <!--=========================-->
      <header class="site-header header-two header_trans-fixed" data-top="992" id="app">
        <div class="container">
          <div class="header-inner">
            <div class="site-mobile-logo">
              <a href="index.html" class="logo">
                <img src="assets/img/logo.png" alt="site logo" class="main-logo" />
                <img src="assets/img/logo.png" alt="site logo" class="sticky-logo" />
              </a>
            </div>
            <!-- /.site-mobile-logo -->

            <div class="toggle-menu">
              <span class="bar"></span>
              <span class="bar"></span>
              <span class="bar"></span>
            </div>

            <!-- /.toggle-menu -->
            <nav class="site-nav nav-two">
              <div class="close-menu">
                <span>Close</span>
                <i class="ei ei-icon_close"></i>
              </div>

              <div class="site-logo">
                <a href="index.html" class="logo">
                  <img src="assets/img/logo.png" alt="site logo" class="main-logo" />
                  <img src="assets/img/logo.png" alt="site logo" class="sticky-logo" />
                </a>
              </div>

              <div class="menu-wrapper" data-top="992">
                <ul class="site-main-menu">
                  <li v-for="list in nav_lists" :key="list.name">
                    <a :href="list.url" style="color: #3730A3;">{{list.name}}</a>
                  </li>
                </ul>

                <div class="nav-right">
                  <a href="https://www.myarkhr.com" class="nav-btn">Sign In</a>
                </div>

                <div class="nav-right">
                  <a href="https://dashboard.myarkhr.com" class="nav-btn">Employee portal</a>
                </div>
              </div>
              <!-- /.menu-wrapper -->
            </nav>

            <!-- /.site-nav -->
          </div>
          <!-- /.header-inner -->
        </div>
        <!-- /.container -->
      </header>
      <!-- /.site-header -->

      <!--==========================-->
      <!--=         Banner         =-->
      <!--==========================-->
      <section class="page-banner-contact">
        <div class="container">
          <div class="row">
            <div class="col-lg-8">
              <div class="page-title-wrapper">
                <div class="page-title-inner">
                  <h1 class="page-title">Terms and Conditions for arkHr</h1>

                  <!-- <p>
                    Why I say old chap that is spiffing, young delinquent in my
                    flat bloke<br />
                    buggered what a plonker.
                  </p> -->
                </div>
                <!-- /.page-title-inner -->
              </div>
              <!-- /.page-title-wrapper -->
            </div>
            <!-- /.col-lg-8 -->

            <div class="col-lg-4">
              <div class="animate-element-contact">
                <img src="media/animated/001.png" alt="" class="wow pixFadeDown" data-wow-duration="1s" />
                <img src="media/animated/002.png" alt="" class="wow pixFadeUp" data-wow-duration="2s" />
                <img src="media/animated/003.png" alt="" class="wow pixFadeLeft" data-wow-delay="0.3s"
                  data-wow-duration="2s" />
                <img src="media/animated/004.png" alt="man" class="wow pixFadeUp" data-wow-duration="2s" />
              </div>
              <!-- /.animate-element-contact -->
            </div>
            <!-- /.col-lg-4 -->
          </div>
          <!-- /.row -->
        </div>
        <!-- /.container -->

        <svg class="circle" data-parallax='{"y" : 250}' xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink" width="950px" height="950px">
          <path fill-rule="evenodd" stroke="#3730A3" stroke-width="100px" stroke-linecap="butt" stroke-linejoin="miter"
            opacity="0.60" fill="none"
            d="M450.000,50.000 C670.914,50.000 850.000,229.086 850.000,450.000 C850.000,670.914 670.914,850.000 450.000,850.000 C229.086,850.000 50.000,670.914 50.000,450.000 C50.000,229.086 229.086,50.000 450.000,50.000 Z" />
        </svg>

        <ul class="animate-ball">
          <li class="ball"></li>
          <li class="ball"></li>
          <li class="ball"></li>
          <li class="ball"></li>
          <li class="ball"></li>
        </ul>
      </section>
      <!-- /.page-banner -->

      <!--===========================-->
      <!--=         Terms and conditions         =-->
      <!--===========================-->
      <div class="terms">

        <div class="">
          <h3>Introduction</h3>
          <p>Welcome to arkHR! These Terms and Conditions ("Terms") govern your use of our platform and services.
            By accessing or using arkHr, you agree to comply with these Terms. If you do not agree, please refrain
            from using our platform. These Terms outline the rules, responsibilities, and legal obligations of both the
            users and arkHr to ensure a smooth and beneficial experience for everyone involved.</p>
        </div>

        <div class="">
          <h3>Definitions</h3>
          <ul>
            <li>"arkHr" refers to our platform, services, and related software, encompassing all functionalities,
              tools, and updates provided.</li>
            <li>"User" or "You" refers to individuals, businesses, or organizations accessing or using arkHr,
              whether for professional, educational, or organizational purposes.</li>
            <li>"Services" include all features provided by arkHr, such as employee management, data analytics,
              payroll management, performance tracking, and other HR solutions tailored to meet diverse needs.</li>
          </ul>
        </div>

        <div class="">
          <h3>Eligibility</h3>
          <p>You must be at least 18 years old to use arkHr. By using our platform, you warrant that you meet this
            requirement and have the legal authority to enter into these Terms. Additionally, organizations using
            arkHr must ensure that all authorized personnel comply with these Terms during their interactions with
            the platform.</p>
        </div>

        <div class="">
          <h3>Use of the Platform</h3>
          <ol>
            <li>Account Creation: You must provide accurate and up-to-date information during registration. Failure to
              do so may result in restrictions or termination of your account.</li>
            <li>Responsibilities: Users are responsible for maintaining the confidentiality of their login credentials
              and all activities under their accounts. Immediate notification of unauthorized access is mandatory.</li>
            <li>Restrictions: You agree not to:
              <ul>
                <li>Use arkHr for unlawful purposes or activities that violate applicable laws and regulations.
                </li>
                <li>Upload malicious software, harmful files, or content that could disrupt platform operations.</li>
                <li>Interfere with the platform's performance, attempt unauthorized access, or exploit system
                  vulnerabilities.</li>
                <li>Share login credentials with unauthorized individuals or entities.</li>
              </ul>
            </li>
          </ol>
        </div>

        <div class="">
          <h3>Subscription and Payment</h3>
          <ol>
            <li>Subscription Plans: arkHr offers a base subscription plan with optional add-ons for additional
              features. Enterprise plans include dedicated servers, databases, and enhanced customer support services.
            </li>
            <li>Billing: Payments are due based on the agreed-upon billing cycle. Late payments may result in suspension
              or restricted access to services until dues are cleared.</li>
            <li>Refunds: Refunds are subject to our refund policy, which may vary by subscription type, and will only be
              processed under specific circumstances as outlined in our policy document.</li>
            <li>Renewals: Subscriptions will automatically renew unless canceled before the end of the billing cycle.
            </li>
          </ol>
        </div>

        <div class="">
          <h3>Data Privacy</h3>
          <p>We value your privacy. Please refer to our Privacy Policy for comprehensive details on how we collect, use,
            store, and protect your information. arkHr adheres to stringent data protection regulations to ensure
            user information is safeguarded.</p>
        </div>

        <div class="">
          <h3>Intellectual Property</h3>
          <ol>
            <li>Ownership: arkHr and its content, including but not limited to software, designs, trademarks,
              documentation, and user guides, are the exclusive property of Clustark.</li>
            <li>License: Users are granted a non-exclusive, non-transferable license to use the platform solely for its
              intended purpose.</li>
            <li>Restrictions: Users may not copy, modify, distribute, reverse-engineer, or exploit ClustarkHR’s
              intellectual property without prior written permission. Violation of these terms may lead to legal action.
            </li>
          </ol>
        </div>

        <div class="">
          <h3>Termination</h3>
          <ol>
            <li>User-Initiated Termination: You may terminate your account at any time through your account settings.
              Upon termination, access to your data may be restricted, and any active subscriptions will cease.</li>
            <li>arkHr-Initiated Termination: We reserve the right to suspend or terminate accounts that violate
              these Terms, engage in fraudulent activities, or disrupt platform operations. Advance notice will be
              provided where feasible.</li>
            <li>Post-Termination Access: Users may request data export within 30 days of termination, subject to
              applicable fees and conditions.</li>
          </ol>
        </div>

        <div class="">
          <h3>Limitation of Liability</h3>
          <p>arkHr is not liable for indirect, incidental, or consequential damages arising from the use or
            inability to use our platform. Our total liability is limited to the amount paid for the subscription in the
            previous billing cycle. Users acknowledge that system downtime, interruptions, or unforeseen technical
            issues are possible and will be addressed promptly without guarantees of compensation.</p>
        </div>

        <div class="">
          <h3>Changes to Terms</h3>
          <p>arkHr reserves the right to modify these Terms at any time. Users will be notified of significant
            changes via email or platform announcements. Continued use of the platform constitutes acceptance of the
            updated Terms. We encourage users to periodically review these Terms for updates.</p>
        </div>

        <div class="">
          <h3>Governing Law</h3>
          <p>These Terms are governed by the laws of Nigeria. Disputes will be resolved in the courts of Nigeria . By
            agreeing to these Terms, users consent to the exclusive jurisdiction of these courts.</p>
        </div>

        <div class="">
          <h3>Contact Us</h3>
          <p>For questions, concerns, or feedback about these Terms, please contact <NAME_EMAIL>. Our
            support team is available to assist with any queries or issues related to the platform.</p>
        </div>
        <hr />

        <div class="">
          <p>By using arkHr, you acknowledge that you have read, understood, and agreed to these Terms and
            Conditions, which outline the foundational rules and expectations for all users. Thank you for choosing
            arkHr as your trusted HR solution.</p>
        </div>

      </div>


      <!--=========================-->
      <!--=        Footer         =-->
      <!--=========================-->
      <footer id="footer" class="footer-two">
        <div class="container">
          <div class="footer-inner wow pixFadeUp">
            <div class="row">
              <div class="col-lg-6 col-md-6">
                <div class="widget footer-widget">
                  <h3 class="widget-title">Company</h3>

                  <ul class="footer-menu">
                    <!-- <li><a href="#">Features</a></li>
                      <li><a href="#">Dashboard & Tool</a></li>
                      <li><a href="#">Our Portfolio</a></li>
                      <li><a href="#">About Us</a></li>
                      <li><a href="#">Get In Touch</a></li> -->

                    <li v-for="list in nav_lists" :key="list.name">
                      <a :href="list.url">{{list.name}}</a>
                    </li>
                  </ul>
                </div>
                <!-- /.widget footer-widget -->
              </div>
              <!-- /.col-lg-3 col-md-6 -->

              <div class="col-lg-6 col-md-6">
                <div class="widget footer-widget">
                  <h3 class="widget-title">Our Address</h3>

                  <p>
                    Lokogoma , Apo , Abuja , Nigeria
                  </p>

                  <ul class="footer-social-link">
                    <li>
                      <a href="http://x.com/clustark_io"><i class="fab fa-twitter"></i></a>
                    </li>
                    <li>
                      <a href="https://www.instagram.com/clustark.io/"><i class="fab fa-instagram"></i></a>
                    </li>
                    <li>
                      <a href="https://www.linkedin.com/company/clustark-hr/"><i class="fab fa-linkedin-in"></i></a>
                    </li>
                  </ul>
                </div>
                <!-- /.widget footer-widget -->
              </div>
              <!-- /.col-lg-3 col-md-6 -->
            </div>
            <!-- /.row -->
          </div>
          <!-- /.footer-inner -->

          <div class="site-info">
            <div class="copyright">
              <p>
                © 2025 All Rights Reserved Design by
                <a href="https://www.myarkhr.com" target="_blank">arkHR</a>
              </p>
            </div>

            <ul class="site-info-menu">
              <!-- <li><a href="#">Privacy & Policy.</a></li>
                <li><a href="#">Faq.</a></li>
                <li><a href="#">Terms.</a></li> -->
            </ul>
          </div>
          <!-- /.site-info -->
        </div>
        <!-- /.container -->
      </footer>
      <!-- /#footer -->
    </div>
  </div>
  <!-- /#site -->

  <!-- Dependency Scripts -->
  <script src="dependencies/jquery/jquery.min.js"></script>
  <script src="dependencies/bootstrap/js/bootstrap.min.js"></script>
  <script src="dependencies/swiper/js/swiper.min.js"></script>
  <script src="dependencies/jquery.appear/jquery.appear.js"></script>
  <script src="dependencies/wow/js/wow.min.js"></script>
  <script src="dependencies/countUp.js/countUp.min.js"></script>
  <script src="dependencies/isotope-layout/isotope.pkgd.min.js"></script>
  <script src="dependencies/imagesloaded/imagesloaded.pkgd.min.js"></script>
  <script src="dependencies/jquery.parallax-scroll/js/jquery.parallax-scroll.js"></script>
  <script src="dependencies/magnific-popup/js/jquery.magnific-popup.min.js"></script>
  <script src="dependencies/gmap3/js/gmap3.min.js"></script>
  <!-- start replicating this code -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.9.0/axios.js"
    integrity="sha512-gtrgGxYyr6TohDyExCZHZ2GQdH+IKY+XWoe+GOEfr88z1U0h+SRaWbUzNm3HhDAL2d8Eu3lFHG/u70e1ODSazA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>
  <script src="assets/js/vue-header.js"></script>
  <!-- end here-->

  <!-- Site Scripts -->
  <script src="assets/js/header.js"></script>
  <script src="assets/js/app.js"></script>

  <script>
    var name = document.getElementById("name").value;
    var email = document.getElementById("email").value;
    var subject = document.getElementById("subject").value;
    var message = document.getElementById("message").value;

    console.log(name, email);

    function sendMail() {
      let data = {
        name: name,
        email: email,
        subject: subject,
        message: message,
      };

      console.log(data);
      return;
      axios
        .post("https://clustark.herokuapp.com/api/v1/contact", data)
        .then((res) => {
          console.log(res);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  </script>
</body>

<!-- Mirrored from saaspik.pixelsigns.art/saaspik/contact.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:30 GMT -->

</html>