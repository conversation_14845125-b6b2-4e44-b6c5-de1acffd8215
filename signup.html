<!DOCTYPE html>
<html lang="en">
  <!-- Mirrored from saaspik.pixelsigns.art/saaspik/signup.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:29 GMT -->
  <head>
    <!-- Meta Data -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
    />
    <title>Sign Up - Clustark</title>

    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/fav/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="assets/img/fav/clustark.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="assets/img/fav/clustark.png"
    />
    <link
      rel="mask-icon"
      href="assets/img/fav/safari-pinned-tab.svg"
      color="#fa7070"
    />

    <meta name="msapplication-TileColor" content="#fa7070" />
    <meta name="theme-color" content="#fa7070" />

    <!-- Dependency Styles -->
    <link
      rel="stylesheet"
      href="dependencies/bootstrap/css/bootstrap.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/fontawesome/css/all.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/swiper/css/swiper.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/wow/css/animate.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/magnific-popup/css/magnific-popup.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/components-elegant-icons/css/elegant-icons.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/simple-line-icons/css/simple-line-icons.css"
      type="text/css"
    />

    <!-- Site Stylesheet -->
    <link rel="stylesheet" href="assets/css/app.css" type="text/css" />

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&amp;family=Satisfy&amp;display=swap"
      rel="stylesheet"
    />
  </head>

  <body id="home-version-1" class="home-version-1" data-style="default">
    <a href="#main_content" data-type="section-switch" class="return-to-top">
      <i class="fa fa-chevron-up"></i>
    </a>

    <div class="page-loader">
      <div class="loader">
        <!-- Loader -->
        <div class="blobs">
          <div class="blob-center"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
          <div class="blob"></div>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
          <defs>
            <filter id="goo">
              <feGaussianBlur
                in="SourceGraphic"
                stdDeviation="10"
                result="blur"
              />
              <feColorMatrix
                in="blur"
                values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                result="goo"
              />
              <feBlend in="SourceGraphic" in2="goo" />
            </filter>
          </defs>
        </svg>
      </div>
    </div>
    <!-- /.page-loader -->

    <div id="main_content">
      <!--=========================-->
      <!--=        Navbar         =-->
      <!--=========================-->
      <header
      class="site-header header-two header_trans-fixed"
      data-top="992"
      id="app"
    >
      <div class="container">
        <div class="header-inner">
          <div class="site-mobile-logo">
            <a href="index.html" class="logo">
              <img
                src="assets/img/logo.png"
                alt="site logo"
                class="main-logo"
              />
              <img
                src="assets/img/logo.png"
                alt="site logo"
                class="sticky-logo"
              />
            </a>
          </div>
          <!-- /.site-mobile-logo -->

          <div class="toggle-menu">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
          </div>

          <!-- /.toggle-menu -->
          <nav class="site-nav nav-two">
            <div class="close-menu">
              <span>Close</span>
              <i class="ei ei-icon_close"></i>
            </div>

            <div class="site-logo">
              <a href="index.html" class="logo">
                <img
                  src="assets/img/logo.png"
                  alt="site logo"
                  class="main-logo"
                />
                <img
                  src="assets/img/logo.png"
                  alt="site logo"
                  class="sticky-logo"
                />
              </a>
            </div>

            <div class="menu-wrapper" data-top="992">
              <ul class="site-main-menu">
                <li v-for="list in nav_lists" :key="list.name">
                  <a :href="list.url">{{list.name}}</a>
                </li>
              </ul>

              <div class="nav-right">
                <a href="signup.html" class="nav-btn">Free Sign Up</a>
              </div>
            </div>
            <!-- /.menu-wrapper -->
          </nav>

          <!-- /.site-nav -->
        </div>
        <!-- /.header-inner -->
      </div>
      <!-- /.container -->
    </header>
      <!-- /.site-header -->

      <!--==========================-->
      <!--=         Sign In         =-->
      <!--==========================-->
      <section class="signin signup">
        <div class="container">
          <div class="row">
            <div class="col-lg-7">
              <div class="signin-from-wrapper">
                <div class="signin-from-inner">
                  <h2 class="title">Signup Now!</h2>

                  <p>
                    With your social network
                  </p>

                  <ul class="singup-social">
                    <li>
                      <a href="#"><i class="fab fa-facebook-f"></i>Facebook</a>
                    </li>
                    <li>
                      <a href="#"><i class="far fa-envelope"></i>Gmail</a>
                    </li>
                    <li>
                      <a href="#"><i class="fab fa-twitter"></i>Twitter</a>
                    </li>
                  </ul>

                  <form action="#" class="singn-form">
                    <input type="text" placeholder="Username" />
                    <input type="text" placeholder="Email" />
                    <input type="password" placeholder="Password" />

                    <div class="forget-link">
                      <div class="condition">
                        <input
                          class="styled-checkbox"
                          id="styled-checkbox-1"
                          type="checkbox"
                          value="value1"
                        />
                        <label for="styled-checkbox-1"></label>
                        <span
                          >I wish to recieve newsletters, promotions and news
                          from.</span
                        >
                      </div>
                    </div>

                    <button type="submit" class="pix-btn">Sign Up</button>

                    <p>
                      Already have an account?
                      <a href="signin.html">Sign in</a> now.
                    </p>
                  </form>
                </div>
                <!-- /.signin-from-inner -->

                <ul class="animate-ball">
                  <li class="ball"></li>
                  <li class="ball"></li>
                  <li class="ball"></li>
                  <li class="ball"></li>
                  <li class="ball"></li>
                </ul>
              </div>
              <!-- /.signin-from-wrapper -->
            </div>
            <!-- /.col-lg-7 -->
          </div>
          <!-- /.row -->
        </div>
        <!-- /.container -->

        <div class="signin-banner signup-banner">
          <div class="animate-image-inner">
            <div class="image-one">
              <img
                src="media/animated/signup.png"
                alt=""
                class="wow pixFadeLeft"
              />
            </div>

            <div class="image-two">
              <img
                src="media/animated/signup2.png"
                alt=""
                class="wow pixFadeRight"
              />
            </div>
            <!-- /.image-two -->
          </div>
          <!-- /.animate-image-inner -->
        </div>
        <!-- /.signin-banner -->
      </section>
      <!-- /.signin -->
    </div>
    <!-- /#site -->

    <!-- Dependency Scripts -->
    <script src="dependencies/jquery/jquery.min.js"></script>
    <script src="dependencies/bootstrap/js/bootstrap.min.js"></script>
    <script src="dependencies/swiper/js/swiper.min.js"></script>
    <script src="dependencies/jquery.appear/jquery.appear.js"></script>
    <script src="dependencies/wow/js/wow.min.js"></script>
    <script src="dependencies/countUp.js/countUp.min.js"></script>
    <script src="dependencies/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="dependencies/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="dependencies/jquery.parallax-scroll/js/jquery.parallax-scroll.js"></script>
    <script src="dependencies/magnific-popup/js/jquery.magnific-popup.min.js"></script>
    <script src="dependencies/gmap3/js/gmap3.min.js"></script>
     <!-- start replicating this code -->
     <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>
     <script src="assets/js/vue-header.js"></script>
     <!-- end here-->
    <script
      type="text/javascript"
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDk2HrmqE4sWSei0XdKGbOMOHN3Mm2Bf-M&amp;ver=2.1.6"
    ></script>

    <!-- Site Scripts -->
    <script src="assets/js/header.js"></script>
    <script src="assets/js/app.js"></script>
  </body>

  <!-- Mirrored from saaspik.pixelsigns.art/saaspik/signup.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:29 GMT -->
</html>
