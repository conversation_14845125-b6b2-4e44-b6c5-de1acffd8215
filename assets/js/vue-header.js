new Vue({
  el: "#app",
  data: {
    name: '',
    email: '',
    message: '',
    subject: '',
    loading: false,
    nav_lists: [
          {name: 'About', url: 'about.html'},
          {name: 'Pricing', url: 'pricing.html'},
          {name: 'Services', url: 'service.html'},
          {name: 'Contact', url: 'contact.html'},
          {name: 'Terms & Conditions', url: 'terms-and-conditions.html'},
    ],
  },
  methods: {
    sendEmail: function() {
      this.loading = true;
      console.log(this.name, this.email, this.message, this.subject);

      const formData = new FormData();
      formData.append('name', this.name);
      formData.append('email', this.email);
      formData.append('message', this.message);
      formData.append('subject', this.subject);

      axios.post('https://api-staging.clustark.io/api/v1/contact/contact', formData)
        .then(response => {
          console.log(response);
          this.name = '';
          this.email = '';
          this.message = '';
          this.subject = '';
          alert('Email sent successfully, we would be in touch');
        })
        .catch(error => {
          console.log(error);
          alert('Error sending email');
        })
        .then(() => {
          this.loading = false;
        });
    }
  }

});

new Vue({
  el: "#footer",
  data: {
   nav_lists: [
        {name: 'Home', url: 'index.html'},
        {name: 'About', url: 'about.html'},
        {name: 'Contact', url: 'contact.html'},
        {name: 'Pricing', url: 'pricing.html'},
        {name: 'Services', url: 'service.html'},
        {name: 'FaQ', url: 'faq.html'},
        {name: 'Terms & Conditions', url: 'terms-and-conditions.html'},
   ],
  },
});


