let monthlyPrice = document.querySelectorAll('.monthly')
    let yearlyPrice = document.querySelectorAll('.yearly')

    const prices = {
      Nigeria: {
        monthly: "₦7,000",
        yearly: "₦70,000"
      },
      default: {
        monthly: "$10",
        yearly: "$100"
      }
    };

    async function fetchLocation() {
      try {
        const response = await fetch("https://ipinfo.io/json?token=733d4ee29c17f3");
        const data = await response.json();

        if (data.country === 'NG') {
          monthlyPrice.forEach(price => price.textContent = prices.Nigeria.monthly);
          yearlyPrice.forEach(price => price.textContent = prices.Nigeria.yearly);
        } else {
          monthlyPrice.forEach(price => price.textContent = prices.default.monthly);
          yearlyPrice.forEach(price => price.textContent = prices.default.yearly);
        }
        // console.log(data.country);

      } catch (error) {
        console.error("Error fetching location:", error.message);
      }
    }

    // Call the function on page load
    fetchLocation();