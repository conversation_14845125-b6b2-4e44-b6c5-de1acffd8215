<!DOCTYPE html>
<html lang="en">
  <!-- Mirrored from saaspik.pixelsigns.art/saaspik/contact.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:29 GMT -->
  <head>
    <!-- Meta Data -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
    />
    <title>Contact - arkHr</title>

    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/fav/clustark.png"
    />

    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="assets/img/fav/clustark.png"
    />

    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="assets/img/fav/clustark.png"
    />

    <link rel="mask-icon" href="assets/img/fav/clustark.png" color="#7052fb" />

    <meta name="msapplication-TileColor" content="#fa7070" />
    <meta name="theme-color" content="#fa7070" />

    <!-- Dependency Styles -->
    <link
      rel="stylesheet"
      href="dependencies/bootstrap/css/bootstrap.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/fontawesome/css/all.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/swiper/css/swiper.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/wow/css/animate.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/magnific-popup/css/magnific-popup.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/components-elegant-icons/css/elegant-icons.min.css"
      type="text/css"
    />
    <link
      rel="stylesheet"
      href="dependencies/simple-line-icons/css/simple-line-icons.css"
      type="text/css"
    />

    <!-- Site Stylesheet -->
    <link rel="stylesheet" href="assets/css/app.css" type="text/css" />

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&amp;family=Satisfy&amp;display=swap"
      rel="stylesheet"
    />

    <!-- google analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-Y7122VDXV0"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-Y7122VDXV0");
    </script>
    <!-- google analytics end -->
  </head>

  <body id="home-version-1" class="home-version-1" data-style="default">
    <div class="" id="app">
      <a href="#main_content" data-type="section-switch" class="return-to-top">
        <i class="fa fa-chevron-up"></i>
      </a>

      <div class="page-loader">
        <div class="loader">
          <!-- Loader -->
          <div class="blobs">
            <div class="blob-center"></div>
            <div class="blob"></div>
            <div class="blob"></div>
            <div class="blob"></div>
            <div class="blob"></div>
            <div class="blob"></div>
            <div class="blob"></div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
            <defs>
              <filter id="goo">
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="10"
                  result="blur"
                />
                <feColorMatrix
                  in="blur"
                  values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                  result="goo"
                />
                <feBlend in="SourceGraphic" in2="goo" />
              </filter>
            </defs>
          </svg>
        </div>
      </div>
      <!-- /.page-loader -->

      <div id="main_content">
        <!--=========================-->
        <!--=        Navbar         =-->
        <!--=========================-->
        <header
          class="site-header header-two header_trans-fixed"
          data-top="992"
          id="app"
        >
          <div class="container">
            <div class="header-inner">
              <div class="site-mobile-logo">
                <a href="index.html" class="logo">
                  <img
                    src="assets/img/logo.png"
                    alt="site logo"
                    class="main-logo"
                  />
                  <img
                    src="assets/img/logo.png"
                    alt="site logo"
                    class="sticky-logo"
                  />
                </a>
              </div>
              <!-- /.site-mobile-logo -->

              <div class="toggle-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
              </div>

              <!-- /.toggle-menu -->
              <nav class="site-nav nav-two">
                <div class="close-menu">
                  <span>Close</span>
                  <i class="ei ei-icon_close"></i>
                </div>

                <div class="site-logo">
                  <a href="index.html" class="logo">
                    <img
                      src="assets/img/logo.png"
                      alt="site logo"
                      class="main-logo"
                    />
                    <img
                      src="assets/img/logo.png"
                      alt="site logo"
                      class="sticky-logo"
                    />
                  </a>
                </div>

                <div class="menu-wrapper" data-top="992">
                  <ul class="site-main-menu">
                    <li v-for="list in nav_lists" :key="list.name">
                      <a :href="list.url" style="color: #3730A3;"
                        >{{list.name}}</a
                      >
                    </li>
                  </ul>

                  <div class="nav-right">
                    <a href="https://www.myarkhr.com" class="nav-btn"
                      >Sign In</a
                    >
                  </div>

                  <div class="nav-right">
                    <a href="https://dashboard.myarkhr.com" class="nav-btn"
                      >Employee portal</a
                    >
                  </div>
                </div>
                <!-- /.menu-wrapper -->
              </nav>

              <!-- /.site-nav -->
            </div>
            <!-- /.header-inner -->
          </div>
          <!-- /.container -->
        </header>
        <!-- /.site-header -->

        <!--==========================-->
        <!--=         Banner         =-->
        <!--==========================-->
        <section class="page-banner-contact">
          <div class="container">
            <div class="row">
              <div class="col-lg-8">
                <div class="page-title-wrapper">
                  <div class="page-title-inner">
                    <h1 class="page-title">Get in touch with Us</h1>

                    <!-- <p>
                    Why I say old chap that is spiffing, young delinquent in my
                    flat bloke<br />
                    buggered what a plonker.
                  </p> -->
                  </div>
                  <!-- /.page-title-inner -->
                </div>
                <!-- /.page-title-wrapper -->
              </div>
              <!-- /.col-lg-8 -->

              <div class="col-lg-4">
                <div class="animate-element-contact">
                  <img
                    src="media/animated/001.png"
                    alt=""
                    class="wow pixFadeDown"
                    data-wow-duration="1s"
                  />
                  <img
                    src="media/animated/002.png"
                    alt=""
                    class="wow pixFadeUp"
                    data-wow-duration="2s"
                  />
                  <img
                    src="media/animated/003.png"
                    alt=""
                    class="wow pixFadeLeft"
                    data-wow-delay="0.3s"
                    data-wow-duration="2s"
                  />
                  <img
                    src="media/animated/004.png"
                    alt="man"
                    class="wow pixFadeUp"
                    data-wow-duration="2s"
                  />
                </div>
                <!-- /.animate-element-contact -->
              </div>
              <!-- /.col-lg-4 -->
            </div>
            <!-- /.row -->
          </div>
          <!-- /.container -->

          <svg
            class="circle"
            data-parallax='{"y" : 250}'
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="950px"
            height="950px"
          >
            <path
              fill-rule="evenodd"
              stroke="#3730A3"
              stroke-width="100px"
              stroke-linecap="butt"
              stroke-linejoin="miter"
              opacity="0.60"
              fill="none"
              d="M450.000,50.000 C670.914,50.000 850.000,229.086 850.000,450.000 C850.000,670.914 670.914,850.000 450.000,850.000 C229.086,850.000 50.000,670.914 50.000,450.000 C50.000,229.086 229.086,50.000 450.000,50.000 Z"
            />
          </svg>

          <ul class="animate-ball">
            <li class="ball"></li>
            <li class="ball"></li>
            <li class="ball"></li>
            <li class="ball"></li>
            <li class="ball"></li>
          </ul>
        </section>
        <!-- /.page-banner -->

        <!--===========================-->
        <!--=         Contact         =-->
        <!--===========================-->
        <section class="contactus">
          <div class="container">
            <div class="row">
              <div class="col-md-4">
                <div class="contact-infos">
                  <div class="contact-info">
                    <h3 class="title">Our Location</h3>
                    <p class="description">
                      Lokogoma , Apo , <br />
                      Abuja , Nigeria
                    </p>

                    <div class="info phone">
                      <i class="ei ei-icon_phone"></i>
                      <span>+2349039011682</span>
                    </div>
                  </div>
                  <!-- /.contact-info -->

                  <div class="contact-info">
                    <!-- <h3 class="title">Say Hello</h3>
                  <p class="description">
                    Washington Fulton Street, Suite 640<br />
                    New York, NY 10010
                  </p> -->

                    <div class="info">
                      <i class="ei ei-icon_mail_alt"></i>
                      <span><EMAIL></span>
                    </div>
                  </div>
                  <!-- /.contact-info -->
                </div>
                <!-- /.contact-infos -->
              </div>
              <!-- /.col-md-4 -->
              <div class="col-md-8" id="contact_vue">
                <div class="contact-froms">
                  <div class="contact-form" data-pixsaas="contact-froms">
                    <div class="row">
                      <div class="col-md-6">
                        <input
                          type="text"
                          name="name"
                          placeholder="Name"
                          required
                          v-model="name"
                        />
                      </div>

                      <div class="col-md-6">
                        <input
                          type="email"
                          name="email"
                          placeholder="Email"
                          required
                          v-model="email"
                        />
                      </div>
                    </div>

                    <input
                      type="text"
                      name="subject"
                      placeholder="Subject"
                      id="subject"
                      v-model="subject"
                    />

                    <textarea
                      name="content"
                      placeholder="Your Comment"
                      required
                      id="message"
                      v-model="message"
                    ></textarea>

                    <button
                      class="pix-btn submit-btn"
                      id="contact-button"
                      @click="sendEmail"
                      :disabled="loading"
                      style="background-color: #7052fb;"
                    >
                      <span class="btn-text"
                        >{{ loading ? 'Loading' : 'Send Your Massage'}}</span
                      >
                      <i class="fas fa-spinner fa-spin"></i>
                    </button>
                    <input
                      type="hidden"
                      name="recaptcha_response"
                      id="recaptchaResponse"
                    />

                    <div class="row">
                      <div class="form-result alert">
                        <div class="content"></div>
                      </div>
                    </div>
                  </div>
                  <!-- /.contact-froms -->
                </div>
                <!-- /.faq-froms -->
              </div>
              <!-- /.col-md-8 -->
            </div>
            <!-- /.row -->
          </div>
          <!-- /.container -->
        </section>
        <!-- /.contactus -->

        <!--========================-->
        <!--=         Map         =-->
        <!--========================-->

        <!-- /#google-maps -->

        <!--=========================-->
        <!--=        Footer         =-->
        <!--=========================-->
        <footer id="footer" class="footer-two">
          <div class="container">
            <div class="footer-inner wow pixFadeUp">
              <div class="row">
                <div class="col-lg-6 col-md-6">
                  <div class="widget footer-widget">
                    <h3 class="widget-title">Company</h3>

                    <ul class="footer-menu">
                      <!-- <li><a href="#">Features</a></li>
                      <li><a href="#">Dashboard & Tool</a></li>
                      <li><a href="#">Our Portfolio</a></li>
                      <li><a href="#">About Us</a></li>
                      <li><a href="#">Get In Touch</a></li> -->

                      <li v-for="list in nav_lists" :key="list.name">
                        <a :href="list.url">{{list.name}}</a>
                      </li>
                    </ul>
                  </div>
                  <!-- /.widget footer-widget -->
                </div>
                <!-- /.col-lg-3 col-md-6 -->

                <div class="col-lg-6 col-md-6">
                  <div class="widget footer-widget">
                    <h3 class="widget-title">Our Address</h3>

                    <p>
                      Lokogoma , Apo , Abuja , Nigeria
                    </p>

                    <ul class="footer-social-link">
                      <li>
                        <a href="http://x.com/clustark_io"
                          ><i class="fab fa-twitter"></i
                        ></a>
                      </li>
                      <li>
                        <a href="https://www.instagram.com/clustark.io/"
                          ><i class="fab fa-instagram"></i
                        ></a>
                      </li>
                      <li>
                        <a href="https://www.linkedin.com/company/clustark-hr/"
                          ><i class="fab fa-linkedin-in"></i
                        ></a>
                      </li>
                    </ul>
                  </div>
                  <!-- /.widget footer-widget -->
                </div>
                <!-- /.col-lg-3 col-md-6 -->
              </div>
              <!-- /.row -->
            </div>
            <!-- /.footer-inner -->

            <div class="site-info">
              <div class="copyright">
                <p>
                  © 2025 All Rights Reserved Design by
                  <a href="https://www.myarkhr.com" target="_blank"
                    >arkHR</a
                  >
                </p>
              </div>

              <ul class="site-info-menu">
                <!-- <li><a href="#">Privacy & Policy.</a></li>
                <li><a href="#">Faq.</a></li>
                <li><a href="#">Terms.</a></li> -->
              </ul>
            </div>
            <!-- /.site-info -->
          </div>
          <!-- /.container -->
        </footer>
        <!-- /#footer -->
      </div>
    </div>
    <!-- /#site -->

    <!-- Dependency Scripts -->
    <script src="dependencies/jquery/jquery.min.js"></script>
    <script src="dependencies/bootstrap/js/bootstrap.min.js"></script>
    <script src="dependencies/swiper/js/swiper.min.js"></script>
    <script src="dependencies/jquery.appear/jquery.appear.js"></script>
    <script src="dependencies/wow/js/wow.min.js"></script>
    <script src="dependencies/countUp.js/countUp.min.js"></script>
    <script src="dependencies/isotope-layout/isotope.pkgd.min.js"></script>
    <script src="dependencies/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="dependencies/jquery.parallax-scroll/js/jquery.parallax-scroll.js"></script>
    <script src="dependencies/magnific-popup/js/jquery.magnific-popup.min.js"></script>
    <script src="dependencies/gmap3/js/gmap3.min.js"></script>
    <!-- start replicating this code -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.9.0/axios.js"
      integrity="sha512-gtrgGxYyr6TohDyExCZHZ2GQdH+IKY+XWoe+GOEfr88z1U0h+SRaWbUzNm3HhDAL2d8Eu3lFHG/u70e1ODSazA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>
    <script src="assets/js/vue-header.js"></script>
    <!-- end here-->

    <!-- Site Scripts -->
    <script src="assets/js/header.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
      var name = document.getElementById("name").value;
      var email = document.getElementById("email").value;
      var subject = document.getElementById("subject").value;
      var message = document.getElementById("message").value;

      console.log(name, email);

      function sendMail() {
        let data = {
          name: name,
          email: email,
          subject: subject,
          message: message,
        };

        console.log(data);
        return;
        axios
          .post("https://clustark.herokuapp.com/api/v1/contact", data)
          .then((res) => {
            console.log(res);
          })
          .catch((err) => {
            console.log(err);
          });
      }
    </script>
  </body>

  <!-- Mirrored from saaspik.pixelsigns.art/saaspik/contact.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Dec 2024 22:18:30 GMT -->
</html>
